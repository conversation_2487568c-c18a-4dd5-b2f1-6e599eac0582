{"options.continuity.title": "Настройки Continuity", "options.continuity.connected_textures": "Соединённые текстуры", "options.continuity.connected_textures.tooltip": "Переключить соединённые текстуры блоков.", "options.continuity.emissive_textures": "Светящиеся текстуры", "options.continuity.emissive_textures.tooltip": "Переключить светящиеся текстуры блоков и моделей предметов.", "options.continuity.custom_block_layers": "Пользов. слои отрисовки", "options.continuity.custom_block_layers.tooltip": "Переключить возможность изменения типа отрисовки для блоков.", "resourcePack.continuity.default.name": "Обычное соединение текстур", "resourcePack.continuity.default.description": "Книжные полки, песчаник и стекло.", "resourcePack.continuity.glass_pane_culling_fix.name": "Исправленные грани панелей", "resourcePack.continuity.glass_pane_culling_fix.description": "Улучшенное отсечение граней стекла", "modmenu.summaryTranslation.continuity": "Эффективные соединённые текстуры для Fabric.", "modmenu.descriptionTranslation.continuity": "Continuity – мод дл<PERSON>, основанный на современных API для обеспечения максимально эффективного опыта взаимодействия. Его предназначение – обеспечить поддержку всех наборов ресурсов, использующих формат соединённых текстур Optifine. Также Continuity поддерживает его формат светящихся текстур у моделей блоков и предметов."}