{"schemaVersion": 1, "id": "continuity", "version": "${version}", "name": "Continuity", "description": "Continuity is a Fabric mod that allows resource packs that use the OptiFine connected textures format, OptiFine emissive textures format (only for blocks and item models), or OptiFine custom block layers format to work without OptiFine.", "authors": ["PepperCode1"], "contact": {"homepage": "https://modrinth.com/mod/continuity", "issues": "https://github.com/PepperCode1/Continuity/issues", "sources": "https://github.com/PepperCode1/Continuity"}, "license": "LGPL-3.0-only", "icon": "assets/continuity/icon.png", "environment": "client", "entrypoints": {"client": ["me.pepperbell.continuity.client.ContinuityClient"], "modmenu": ["me.pepperbell.continuity.client.config.ModMenuApiImpl"]}, "mixins": ["continuity.mixins.json"], "depends": {"minecraft": ">=1.21 <=1.21.1", "fabricloader": ">=0.15.0", "fabric-api": ">=0.100.1"}, "custom": {"modmenu": {"links": {"modmenu.discord": "https://discord.gg/7rnTYXu"}}}}