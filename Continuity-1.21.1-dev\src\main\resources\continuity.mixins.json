{"required": true, "minVersion": "0.8", "package": "me.pepperbell.continuity.client.mixin", "compatibilityLevel": "JAVA_21", "client": ["AtlasLoaderMixin", "BakedModelManagerMixin", "BlockModelsMixin", "ClientMixin", "ClientPlayerEntityMixin", "FallingBlockEntityRendererMixin", "IdentifierMixin", "ItemUsageMixin", "LifecycledResourceManagerImplMixin", "ModelLoaderMixin", "NamespaceResourceManagerMixin", "PistonBlockEntityRendererMixin", "ReloadableResourceManagerImplAccessor", "RenderLayersMixin", "SpriteLoaderMixin", "SpriteMixin"], "injectors": {"defaultRequire": 1}}