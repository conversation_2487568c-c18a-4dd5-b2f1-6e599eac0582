{"required": true, "minVersion": "0.8", "package": "me.pepperbell.continuity.client.mixin", "compatibilityLevel": "JAVA_21", "client": ["AtlasLoaderMixin", "BakedModelManagerMixin", "BlockModelsMixin", "ClientMixin", "ClientPlayerEntityMixin", "FallingBlockEntityRendererMixin", "ItemUsageMixin", "LayerRenderStateMixin", "LifecycledResourceManagerImplMixin", "PistonBlockEntityRendererMixin", "RenderLayersMixin", "SpriteLoaderMixin", "SpriteMixin"], "injectors": {"defaultRequire": 1}}