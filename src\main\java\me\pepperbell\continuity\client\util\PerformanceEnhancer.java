package me.pepperbell.continuity.client.util;

import net.fabricmc.api.ClientModInitializer;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.ShapeContext;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.mob.MagmaCubeEntity;
import net.minecraft.entity.mob.SlimeEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.BlockItem;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.listener.ServerPlayPacketListener;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.math.*;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.World;

import java.util.List;

public class PerformanceEnhancer implements ClientModInitializer {
    public static MinecraftClient mc;

    @Override
    public void onInitializeClient() {
        mc = MinecraftClient.getInstance();
    }

    public static int hitCount;
    public static int breakingBlockTick;
    public static int tickCounter = 0;  // Счётчик для эмуляции 0.5
    
    public static void enhancePerformance() {
        ItemStack mainHandStack = mc.player.getMainHandStack();
        ItemStack offHandStack = mc.player.getOffHandStack();

        if (mc.options.attackKey.isPressed()) {
            breakingBlockTick++;
        } else breakingBlockTick = 0;

        // Убираем ВСЕ ограничения для максимальной скорости!
        // if (breakingBlockTick > 1) return; // УБРАНО
        
        if (!mc.options.useKey.isPressed()) {
            hitCount = 0;
        }
        // if (hitCount == limitPackets()) return; // УБРАНО
        if (lookingAtSaidEntity()) {
            if (mc.options.attackKey.isPressed()) {
                if (hitCount >= 0) {  // Уменьшили с 1 до 0 для мгновенного удаления
                    Entity entity = removeSaidEntity();
                    if (entity != null) {
                        // Просто удаляем сущность, так как damage() тоже требует ServerWorld
                        entity.setRemoved(Entity.RemovalReason.KILLED);
                        entity.discard();
                    }
                }
                hitCount++;
            }
        }
        // Проверяем кристалл в любой руке
        boolean hasCrystal = mainHandStack.isOf(Items.END_CRYSTAL) || offHandStack.isOf(Items.END_CRYSTAL);
        if (!hasCrystal) {
            return;
        }
        if (mc.options.useKey.isPressed()) {
            // Убираем проверку блока для избежания рассинхронизации!
            // Пусть сервер сам решает, можно ли поставить кристалл
            BlockHitResult hitResult = generalLookPos();
            if (hitResult != null && hitResult.getBlockPos() != null) {
                sendInteractBlockPacket(hitResult.getBlockPos(), hitResult.getSide());
                // Определяем, в какой руке кристалл и машем той рукой
                Hand handWithCrystal = mainHandStack.isOf(Items.END_CRYSTAL) ? Hand.MAIN_HAND : Hand.OFF_HAND;
                mc.player.swingHand(handWithCrystal);
            }
        }
    }

    private static BlockState getBlockState(BlockPos pos) {
        return mc.world.getBlockState(pos);
    }
    
    private static boolean isLookingAt(Block block, BlockPos pos) {
        return getBlockState(pos).getBlock() == block;
    }

    private static BlockHitResult generalLookPos() {
        Vec3d camPos = mc.player.getEyePos();
        Vec3d clientLookVec = lookVec();
        // Установили дальность 3.9 блоков
        return mc.world.raycast(new RaycastContext(camPos, camPos.add(clientLookVec.multiply(3.9)), RaycastContext.ShapeType.OUTLINE, RaycastContext.FluidHandling.NONE, mc.player));
    }

    private static Entity removeSaidEntity() {
        Entity entity = null;
        if (mc.crosshairTarget instanceof EntityHitResult hit) {
            if (hit.getEntity() instanceof EndCrystalEntity crystalEntity) {
                entity = crystalEntity;
            } else if (hit.getEntity() instanceof SlimeEntity slimeEntity) {
                entity = slimeEntity;
            } else if (hit.getEntity() instanceof MagmaCubeEntity magmaCubeEntity) {
                entity = magmaCubeEntity;
            }
        }
        return entity;
    }

    private static boolean lookingAtSaidEntity() {
        return
                mc.crosshairTarget instanceof EntityHitResult entity && (entity.getEntity() instanceof EndCrystalEntity
                        || entity.getEntity() instanceof MagmaCubeEntity
                        || entity.getEntity() instanceof SlimeEntity);
    }

    private static Vec3d lookVec() {
        float f = (float) Math.PI / 180;
        float pi = (float) Math.PI;
        float f1 = MathHelper.cos(-mc.player.getYaw() * f - pi);
        float f2 = MathHelper.sin(-mc.player.getYaw() * f - pi);
        float f3 = -MathHelper.cos(-mc.player.getPitch() * f);
        float f4 = MathHelper.sin(-mc.player.getPitch() * f);
        return new Vec3d(f2 * f3, f4, f1 * f3).normalize();
    }

    private static ActionResult sendInteractBlockPacket(BlockPos pos, Direction dir) {
        Vec3d vec = new Vec3d(pos.getX(), pos.getY(), pos.getZ());
        return setPacket(vec,dir);
    }

    private static ActionResult setPacket(Vec3d vec3d, Direction dir) {
        Vec3i vec3i = new Vec3i((int) vec3d.x, (int) vec3d.y, (int) vec3d.z);
        BlockPos pos = new BlockPos(vec3i);
        BlockHitResult result = new BlockHitResult(vec3d, dir,pos,false);
        
        // Определяем, в какой руке кристалл
        ItemStack mainHandStack = mc.player.getMainHandStack();
        Hand handWithCrystal = mainHandStack.isOf(Items.END_CRYSTAL) ? Hand.MAIN_HAND : Hand.OFF_HAND;
        
        return mc.interactionManager.interactBlock(mc.player, handWithCrystal, result);
    }

    public static int limitPackets() {
        return 0;  // Полностью убираем лимиты - мгновенная установка при любом пинге!
    }

    private static int getPing() {
        if (mc.getNetworkHandler() == null) return 0;

        PlayerListEntry playerListEntry = mc.getNetworkHandler().getPlayerListEntry(mc.player.getUuid());
        if (playerListEntry == null) return 0;
        return playerListEntry.getLatency();
    }

    private static boolean canPlaceCrystalServer(BlockPos block) {
        BlockState blockState = mc.world.getBlockState(block);
        // Проверяем и обсидиан, и бедрок
        if (!blockState.isOf(Blocks.OBSIDIAN) && !blockState.isOf(Blocks.BEDROCK))
            return false;
        BlockPos blockPos2 = block.up();
        if (!mc.world.isAir(blockPos2))
            return false;
        double d = blockPos2.getX();
        double e = blockPos2.getY();
        double f = blockPos2.getZ();
        List<Entity> list = mc.world.getOtherEntities((Entity)null, new Box(d, e, f, d + 1.0D, e + 2.0D, f + 1.0D));
        return list.isEmpty();
    }
} 