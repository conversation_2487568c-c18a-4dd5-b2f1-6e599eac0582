package me.pepperbell.continuity.client.util;

import net.fabricmc.api.ClientModInitializer;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.mob.MagmaCubeEntity;
import net.minecraft.entity.mob.SlimeEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.math.*;
import net.minecraft.world.RaycastContext;

public class PerformanceEnhancer implements ClientModInitializer {
    public static MinecraftClient mc;

    @Override
    public void onInitializeClient() {
        mc = MinecraftClient.getInstance();
    }

    public static int hitCount;
    public static int breakingBlockTick;
    
    public static void enhancePerformance() {
        ItemStack mainHandStack = mc.player.getMainHandStack();
        ItemStack offHandStack = mc.player.getOffHandStack();

        if (mc.options.attackKey.isPressed()) {
            breakingBlockTick++;
        } else breakingBlockTick = 0;

        if (breakingBlockTick > 1)
            return;

        if (!mc.options.useKey.isPressed()) {
            hitCount = 0;
        }
        if (hitCount == limitPackets())
            return;
        if (lookingAtSaidEntity()) {
            if (mc.options.attackKey.isPressed()) {
                if (hitCount >= 0) {
                    Entity entity = removeSaidEntity();
                    if (entity != null) {
                        entity.setRemoved(Entity.RemovalReason.KILLED);
                        entity.discard();
                    }
                }
                hitCount++;
            }
        }
        // Проверяем кристалл в любой руке
        boolean hasCrystal = mainHandStack.isOf(Items.END_CRYSTAL) || offHandStack.isOf(Items.END_CRYSTAL);
        if (!hasCrystal) {
            return;
        }
        if (mc.options.useKey.isPressed()
                && (isLookingAt(Blocks.OBSIDIAN, generalLookPos().getBlockPos()) 
                    || isLookingAt(Blocks.BEDROCK, generalLookPos().getBlockPos())))
        {
            sendInteractBlockPacket(generalLookPos().getBlockPos(), generalLookPos().getSide());
            // Определяем, в какой руке кристалл и машем той рукой
            Hand handWithCrystal = mainHandStack.isOf(Items.END_CRYSTAL) ? Hand.MAIN_HAND : Hand.OFF_HAND;
            mc.player.swingHand(handWithCrystal);
        }
    }

    private static BlockState getBlockState(BlockPos pos) {
        return mc.world.getBlockState(pos);
    }
    
    private static boolean isLookingAt(Block block, BlockPos pos) {
        return getBlockState(pos).getBlock() == block;
    }

    private static BlockHitResult generalLookPos() {
        Vec3d camPos = mc.player.getEyePos();
        Vec3d clientLookVec = lookVec();
        return mc.world.raycast(new RaycastContext(camPos, camPos.add(clientLookVec.multiply(3.9)), RaycastContext.ShapeType.OUTLINE, RaycastContext.FluidHandling.NONE, mc.player));
    }

    private static Entity removeSaidEntity() {
        Entity entity = null;
        if (mc.crosshairTarget instanceof EntityHitResult hit) {
            if (hit.getEntity() instanceof EndCrystalEntity crystalEntity) {
                entity = crystalEntity;
            } else if (hit.getEntity() instanceof SlimeEntity slimeEntity) {
                entity = slimeEntity;
            } else if (hit.getEntity() instanceof MagmaCubeEntity magmaCubeEntity) {
                entity = magmaCubeEntity;
            }
        }
        return entity;
    }

    private static boolean lookingAtSaidEntity() {
        return
                mc.crosshairTarget instanceof EntityHitResult entity && (entity.getEntity() instanceof EndCrystalEntity
                        || entity.getEntity() instanceof MagmaCubeEntity
                        || entity.getEntity() instanceof SlimeEntity);
    }

    private static Vec3d lookVec() {
        float f = (float) Math.PI / 180;
        float pi = (float) Math.PI;
        float f1 = MathHelper.cos(-mc.player.getYaw() * f - pi);
        float f2 = MathHelper.sin(-mc.player.getYaw() * f - pi);
        float f3 = -MathHelper.cos(-mc.player.getPitch() * f);
        float f4 = MathHelper.sin(-mc.player.getPitch() * f);
        return new Vec3d(f2 * f3, f4, f1 * f3).normalize();
    }

    private static ActionResult sendInteractBlockPacket(BlockPos pos, Direction dir) {
        Vec3d vec = new Vec3d(pos.getX(), pos.getY(), pos.getZ());
        return setPacket(vec,dir);
    }

    private static ActionResult setPacket(Vec3d vec3d, Direction dir) {
        Vec3i vec3i = new Vec3i((int) vec3d.x, (int) vec3d.y, (int) vec3d.z);
        BlockPos pos = new BlockPos(vec3i);
        BlockHitResult result = new BlockHitResult(vec3d, dir,pos,false);
        
        // Определяем, в какой руке кристалл
        ItemStack mainHandStack = mc.player.getMainHandStack();
        Hand handWithCrystal = mainHandStack.isOf(Items.END_CRYSTAL) ? Hand.MAIN_HAND : Hand.OFF_HAND;
        
        return mc.interactionManager.interactBlock(mc.player, handWithCrystal, result);
    }

    public static int limitPackets() {
        int stop = 1;
        if (getPing() > 50) stop = 2;
        if (getPing() < 50) stop = 1;
        return stop;
    }

    private static int getPing() {
        if (mc.getNetworkHandler() == null) return 0;

        PlayerListEntry playerListEntry = mc.getNetworkHandler().getPlayerListEntry(mc.player.getUuid());
        if (playerListEntry == null) return 0;
        return playerListEntry.getLatency();
    }
}