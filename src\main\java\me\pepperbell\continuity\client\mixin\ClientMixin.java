package me.pepperbell.continuity.client.mixin;

import net.minecraft.client.MinecraftClient;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import me.pepperbell.continuity.client.util.PerformanceEnhancer;

import static me.pepperbell.continuity.client.util.PerformanceEnhancer.limitPackets;
import static me.pepperbell.continuity.client.util.PerformanceEnhancer.mc;

@Mixin(MinecraftClient.class)
public abstract class ClientMixin {
    
    @Inject(at = @At("HEAD"), method = "doItemUse", cancellable = true)
    private void onDoItemUse(CallbackInfo ci) {
        ItemStack mainHand = mc.player.getMainHandStack();
        if (mainHand.isOf(Items.END_CRYSTAL))
            if (PerformanceEnhancer.hitCount != limitPackets())
                ci.cancel();
    }
} 