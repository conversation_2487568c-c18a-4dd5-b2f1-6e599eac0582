plugins {
	id 'fabric-loom' version "${loom_version}"
	id 'maven-publish'
}

version = project.mod_version + '+' + project.mod_minecraft_version + getExtraBuildMetadata()
group = project.maven_group

base {
	archivesName = project.archives_base_name
}

static String getExtraBuildMetadata() {
	String buildNumber = System.getenv('GITHUB_RUN_NUMBER')
	if (buildNumber != null) {
		return ".build.${buildNumber}"
	}
	return ''
}

repositories {
	maven {
		name 'TerraformersMC'
		url 'https://maven.terraformersmc.com'
	}
	maven {
		name 'Modrinth'
		url 'https://api.modrinth.com/maven'
	}
	maven {
		name '<PERSON><PERSON><PERSON>'
		url 'https://maven.shedaniel.me'
	}
}

dependencies {
	// To change the versions see the gradle.properties file
	minecraft "com.mojang:minecraft:${project.minecraft_version}"
	mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
	modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

	// Fabric API
	modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"

	modImplementation("com.terraformersmc:modmenu:${modmenu_version}") {
		exclude group: 'net.fabricmc.fabric-api'
	}
}

processResources {
	inputs.property 'version', project.version

	filesMatching('fabric.mod.json') {
		expand 'version': project.version
	}
}

tasks.withType(JavaCompile).configureEach {
	it.options.release = 21
}

java {
	// Loom will automatically attach sourcesJar to a RemapSourcesJar task and to the "build" task
	// if it is present.
	// If you remove this line, sources will not be generated.
	withSourcesJar()

	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

jar {
	from('LICENSE') {
		rename { "${it}_${project.base.archivesName.get()}" }
	}
}

// configure the maven publication
publishing {
	publications {
		create('mavenJava', MavenPublication) {
			artifactId = project.archives_base_name
			from components.java
		}
	}

	// See https://docs.gradle.org/current/userguide/publishing_maven.html for information on how to set up publishing.
	repositories {
		// Add repositories to publish to here.
		// Notice: This block does NOT have the same function as the block in the top level.
		// The repositories here will be used for publishing your artifact, not for
		// retrieving dependencies.
	}
}
