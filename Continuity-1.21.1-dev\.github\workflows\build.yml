name: Java CI with Gradle

on: [ push ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
          check-latest: true
      - name: Build artifacts
        run: ./gradlew build --stacktrace
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: build/libs/
